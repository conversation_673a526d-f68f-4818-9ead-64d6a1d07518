import { useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef } from "react";
import { toast } from "sonner";

import type { ApiClient } from "@pearl/api-client";
import type { Integration } from "@pearl/api-hooks";
import { useGetSalesforceAuthUrl } from "@pearl/api-hooks";

import { handleRedirectOAuthSuccess } from "~/features/integrations/hooks/useIntegrationActions";
import type { IntegrationStrategy } from "~/features/integrations/types";

interface Extra {
  authUrl: string;
}

const useSalesforceAutoCallback = () => {
  const queryClient = useQueryClient();
  const hasProcessed = useRef(false);

  useEffect(() => {
    const processCallback = async () => {
      if (hasProcessed.current) return;

      const currentUrl = new URL(window.location.href);
      const code = currentUrl.searchParams.get("code");
      const state = currentUrl.searchParams.get("state");

      if (
        code &&
        state &&
        currentUrl.pathname === "/onboarding/salesforce/callback"
      ) {
        hasProcessed.current = true;

        try {
          const { apiClient } = await import("~/services/api/ApiClient");

          const encodedCode = encodeURIComponent(code);
          const encodedState = encodeURIComponent(state);

          await apiClient.get(
            `/workspace/salesforce/callback?code=${encodedCode}&state=${encodedState}`,
          );

          await apiClient.get("/workspace/sync_accounts");

          const newSearchParams = new URLSearchParams(currentUrl.search);
          newSearchParams.delete("code");
          newSearchParams.delete("state");

          const newUrl = newSearchParams.toString()
            ? `/onboarding/salesforce/callback?${newSearchParams.toString()}`
            : "/onboarding/salesforce/callback";

          window.history.replaceState({}, "", newUrl);

          await handleRedirectOAuthSuccess("salesforce", queryClient);
        } catch (error) {
          console.error("Salesforce callback error:", error);
          toast.error("Failed to connect Salesforce. Please try again.");
        }
      }
    };

    void processCallback();
  }, [queryClient]);
};

export const salesforceStrategy: IntegrationStrategy<Extra> = {
  id: "salesforce",
  displayName: "Salesforce",
  oauthFlowType: "redirect",

  useExtraData: () => {
    // Use the auto-callback hook to process OAuth callbacks automatically
    useSalesforceAutoCallback();

    return useGetSalesforceAuthUrl();
  },

  connect: ({
    extra,
  }: {
    apiClient: ApiClient;
    extra: Extra | undefined;
    integration: Integration;
  }) =>
    new Promise<void>((_, reject) => {
      if (!extra?.authUrl) {
        reject(new Error("No auth URL available"));
        return;
      }

      window.location.href = extra.authUrl;
    }),
};
